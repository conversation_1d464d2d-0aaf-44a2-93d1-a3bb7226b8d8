import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

struct LoginView: View {
    @EnvironmentObject var authService: AuthService
    @FocusState private var focusedField: String?
    
    @State private var username: String = ""
    @State private var password: String = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showRegisterView = false
    @State private var showForgotPasswordView = false
    @State private var logoPosition: CGFloat = 0.5 // 控制Logo的相对位置（0-1之间）
    @State private var showLoginContent = false // 控制登录内容的显示

    var onLoginSuccess: (() -> Void)?

    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .top) {
                // 背景色填满整个屏幕
                Color.primaryBg
                    .edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        hideKeyboard()
                    }
                
                VStack(spacing: 0) {
                    // Logo和所有内容区域
                    VStack(spacing: 0) {
                        // 动态Spacer，根据logoPosition调整大小
                        Spacer()
                            .frame(height: geometry.size.height * logoPosition)
                        
                        // Logo区域
                        Image("brewlog-chn-logo")
                            .renderingMode(.template)
                            .resizable()
                            .scaledToFit()
                            .frame(height: 30)
                            .foregroundColor(.functionText)
                            .padding(.top, 20)
                            .padding(.bottom, 30)
                        
                        if showLoginContent {
                            // 登录表单内容，使用淡入效果显示
                            VStack(spacing: 16) {
                                // 登录表单区域
                                loginFormSection

                                // 错误信息区域
                                if let errorMessage = errorMessage {
                                    errorSection(errorMessage)
                                }

                                // 登录按钮区域
                                loginButtonSection

                                // 注册和找回密码区域
                                additionalActionsSection
                            }
                            .padding(.horizontal, 12)
                            .transition(.opacity)
                        }
                        
                        // 添加底部空间，确保忘记密码按钮与屏幕底部保持一个图标的高度
                        Spacer(minLength: 30)
                    }
                }
                .frame(width: geometry.size.width, height: geometry.size.height)
            }
            .onAppear {
                // 先延迟一小段时间，确保视图已完全加载
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    // 执行Logo移动动画，从屏幕中心移动到顶部1/5处
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
                        // 从0.5（中心）移动到0.2（顶部1/5处）
                        logoPosition = 0.2
                    }
                    
                    // Logo移动后，显示登录内容
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                        withAnimation(.easeIn(duration: 0.3)) {
                            showLoginContent = true
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showRegisterView) {
            RegisterView(onRegisterSuccess: onLoginSuccess)
                .interactiveDismissDisabled(true)
        }
        .sheet(isPresented: $showForgotPasswordView) {
            ForgotPasswordView()
                .presentationDragIndicator(.visible)
        }
        .overlay(
            Group {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.4)
                            .edgesIgnoringSafeArea(.all)

                        BlinkingLoader(
                            color: .primaryBg,
                            width: 12,
                            height: 18,
                            duration: 1.5,
                            text: "登录中..."
                        )
                        .padding()
                        .background(Color.black.opacity(0.5))
                        .cornerRadius(10)
                        .shadow(radius: 5)
                    }
                }
            }
        )
    }

    // 登录表单区域
    private var loginFormSection: some View {
        VStack(spacing: 20) {
            formFieldSimple(title: "用户名", binding: $username, placeholder: "请输入用户名或邮箱")
                .focused($focusedField, equals: "用户名_field")
            
            formFieldSimple(title: "密码", binding: $password, placeholder: "请输入密码", isSecure: true)
                .focused($focusedField, equals: "密码_field")
        }
        .padding(.top, 16)
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 错误信息区域
    private func errorSection(_ message: String) -> some View {
        VStack(spacing: 16) {
            HStack(spacing: 8) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.error)
                Text("登录失败")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.error)
                Spacer()
            }

            Text(message)
                .font(.system(size: 15))
                .foregroundColor(.primaryText)
                .frame(maxWidth: .infinity, alignment: .leading)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 登录按钮区域
    private var loginButtonSection: some View {
        VStack(spacing: 16) {
            Button(action: login) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .frame(width: 16, height: 16)
                            .progressViewStyle(CircularProgressViewStyle(tint: .primaryBg))
                    } else {
                        Image(systemName: "lock.open")
                    }
                    Text("登录")
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primaryBg)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background((!username.isEmpty && !password.isEmpty && !isLoading) ? Color.functionText : Color.gray.opacity(0.5))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(isLoading || username.isEmpty || password.isEmpty)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 注册和找回密码区域
    private var additionalActionsSection: some View {
        VStack(spacing: 16) {
            Button(action: {
                showRegisterView = true
            }) {
                HStack {
                    Image("newAccount.symbols")
                    Text("还没有账号？点我立即注册")
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.linkText)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(Color.secondaryBg)
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())

            Button(action: {
                showForgotPasswordView = true
            }) {
                HStack {
                    Image("forgetPassword.symbols")
                    Text("忘记密码了？点我找回密码")
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.functionText)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(Color.secondaryBg)
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 简化版表单字段
    private func formFieldSimple(title: String, binding: Binding<String>, placeholder: String? = nil, isSecure: Bool = false) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.primaryText)

            if isSecure {
                SecureField(placeholder ?? "请输入\(title)", text: binding)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .disableAutocorrection(true)
                    .autocapitalization(.none)
                    .submitLabel(.done)
                    .textContentType(.password)
            } else {
                TextField(placeholder ?? "请输入\(title)", text: binding)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .disableAutocorrection(true)
                    .autocapitalization(.none)
                    .submitLabel(.next)
                    .textContentType(.username)
            }
        }
    }

    // 隐藏键盘方法
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    private func login() {
        isLoading = true
        errorMessage = nil
        
        // 隐藏键盘
        hideKeyboard()

        Task {
            do {
                try await authService.login(username: username, password: password)
                await MainActor.run {
                    isLoading = false
                    onLoginSuccess?()
                }
            } catch let authError as AuthError {
                // 特别处理AuthError类型的错误
                await MainActor.run {
                    isLoading = false
                    // 直接使用错误的本地化描述
                    errorMessage = authError.localizedDescription
                    
                    print("⚠️ 登录错误类型: AuthError.\(String(describing: authError))")
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "\(error.localizedDescription)"
                    
                    print("⚠️ 登录错误类型: \(String(describing: type(of: error)))")
                }
            }
        }
    }
}

// 为Preview创建一个模拟的AuthService
#if DEBUG
extension AuthService {
    static var preview: AuthService {
        // 使用共享实例，避免直接初始化
        let shared = AuthService.shared
        return shared
    }
}
#endif

#Preview {
    LoginView()
        .environmentObject(AuthService.preview)
}
